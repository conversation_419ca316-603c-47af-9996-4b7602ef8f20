"use client";

import { ReactNode } from "react";
import MainNavbar from "./Navbar";
import Footer from "./Footer";

interface MainLayoutProps {
  children: ReactNode;
  className?: string;
}

export default function MainLayout({ children, className = "" }: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <MainNavbar />
      <main className={`flex-1 pt-[72px] ${className}`}>
        {children}
      </main>
      <Footer />
    </div>
  );
}
