"use client";

import {
  Navbar,
  Nav<PERSON><PERSON><PERSON>,
  NavbarContent,
  NavbarItem,
  NavbarMenuToggle,
  NavbarMenu,
  NavbarMenuItem,
  Link,
  Button,
} from "@heroui/react";
import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { useTranslations, useLocale } from "next-intl";
import { useRouter, usePathname } from "next/navigation";
import { Globe, Sun, Moon, Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface NavItem {
  labelKey: string;
  href: string;
}

const navigationItems: NavItem[] = [
  { labelKey: "navigation.home", href: "/" },
  { labelKey: "navigation.company", href: "/pds-group" },
  { labelKey: "navigation.services", href: "/products-services" },
  { labelKey: "navigation.references", href: "/reference" },
  { labelKey: "navigation.news", href: "/news-events" },
  { labelKey: "navigation.contact", href: "/contact-us" },
];

export default function MainNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { theme, setTheme } = useTheme();
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleLanguage = () => {
    const newLocale = locale === "en" ? "th" : "en";
    const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <motion.div
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50"
    >
      <Navbar
        onMenuOpenChange={setIsMenuOpen}
        className={`transition-all duration-300 ${isScrolled
          ? "bg-white/98 backdrop-blur-sm shadow-sm border-b border-gray-100"
          : "bg-white/95 backdrop-blur-sm"
          }`}
        maxWidth="xl"
        height="80px"
      >
        {/* Brand */}
        <NavbarContent>
          <div className="sm:hidden">
            <Button
              isIconOnly
              variant="light"
              onPress={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 dark:text-gray-300"
              aria-label={isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
            >
              <AnimatePresence mode="wait">
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X size={24} />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu size={24} />
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </div>
          <NavbarBrand>
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">PDS</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-gray-900">
                  PADUNGSILPA
                </span>
                <span className="text-xs text-gray-500 font-medium tracking-wider">
                  GROUP
                </span>
              </div>
            </Link>
          </NavbarBrand>
        </NavbarContent>

        {/* Desktop Navigation */}
        <NavbarContent className="hidden sm:flex gap-8" justify="center">
          {navigationItems.map((item) => (
            <NavbarItem key={item.href}>
              <Link
                href={`/${locale}${item.href}`}
                className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2"
              >
                {t(item.labelKey)}
              </Link>
            </NavbarItem>
          ))}
        </NavbarContent>

        {/* Right Side Actions */}
        <NavbarContent justify="end" className="gap-3">
          {/* Language Switcher */}
          <NavbarItem>
            <Button
              isIconOnly
              variant="light"
              onPress={toggleLanguage}
              className="text-gray-600 hover:text-blue-600 hover:bg-gray-100 transition-colors duration-200"
            >
              <Globe size={18} />
            </Button>
          </NavbarItem>

          {/* Contact Button */}
          <NavbarItem className="hidden lg:flex">
            <Button
              as={Link}
              href={`/${locale}/contact-us`}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded-lg transition-colors duration-200"
            >
              {t("common.contactUs")}
            </Button>
          </NavbarItem>
        </NavbarContent>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <NavbarMenu className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-t border-gray-200/20 dark:border-gray-700/20">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="pt-4"
              >
                {navigationItems.map((item, index) => (
                  <NavbarMenuItem key={`${item.href}-${index}`}>
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Link
                        href={`/${locale}${item.href}`}
                        className="w-full text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 py-4 text-lg font-medium border-b border-gray-200/50 dark:border-gray-700/50 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all duration-300 rounded-lg px-4"
                        onPress={() => setIsMenuOpen(false)}
                      >
                        {t(item.labelKey)}
                      </Link>
                    </motion.div>
                  </NavbarMenuItem>
                ))}
                <NavbarMenuItem>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.6 }}
                    className="mt-6"
                  >
                    <Button
                      as={Link}
                      href={`/${locale}/contact-us`}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 rounded-full shadow-lg"
                      onPress={() => setIsMenuOpen(false)}
                    >
                      {t("common.contactUs")}
                    </Button>
                  </motion.div>
                </NavbarMenuItem>
              </motion.div>
            </NavbarMenu>
          )}
        </AnimatePresence>
      </Navbar>
    </motion.div>
  );
}
