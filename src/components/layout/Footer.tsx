"use client";

import { <PERSON>, <PERSON><PERSON>, Divider } from "@heroui/react";
import {
  MapPin,
  Phone,
  Mail,
  Facebook,
  Instagram,
  Linkedin,
  Youtube,
  Clock,
  Globe,
  ArrowUp
} from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";

interface FooterLink {
  label: string;
  href: string;
  labelTh: string;
}

const quickLinks: FooterLink[] = [
  { label: "About Us", href: "/pds-group", labelTh: "เกี่ยวกับเรา" },
  { label: "Products & Services", href: "/products-services", labelTh: "สินค้าและบริการ" },
  { label: "References", href: "/reference", labelTh: "ผลงาน" },
  { label: "News & Events", href: "/news-events", labelTh: "ข่าวสารและกิจกรรม" },
];

const services: FooterLink[] = [
  { label: "Gas Station Services", href: "/products-services#gas-station", labelTh: "บริการสถานีบริการน้ำมัน" },
  { label: "Construction", href: "/products-services#construction", labelTh: "งานก่อสร้าง" },
  { label: "Engineering", href: "/products-services#engineering", labelTh: "งานวิศวกรรม" },
  { label: "Consulting", href: "/products-services#consulting", labelTh: "งานที่ปรึกษา" },
];

export default function Footer() {
  const [currentLang, setCurrentLang] = useState<'en' | 'th'>('th');
  const t = useTranslations();

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <footer className="relative bg-gradient-to-br from-gray-900 via-slate-900 to-gray-900 text-white overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      {/* Main Footer Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="lg:col-span-1"
          >
            <div className="flex items-center space-x-3 mb-6">
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                className="w-14 h-14 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg"
              >
                <span className="text-white font-bold text-xl">PDS</span>
              </motion.div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  PADUNGSILPA
                </span>
                <span className="text-sm text-gray-400 font-medium tracking-wider">
                  GROUP
                </span>
              </div>
            </div>

            <p className="text-gray-300 mb-8 leading-relaxed">
              {currentLang === 'en'
                ? "Leading comprehensive gas station business services with over 20 years of experience in construction and engineering."
                : "ผู้นำด้านธุรกิจสถานีบริการน้ำมันครบวงจร ด้วยประสบการณ์กว่า 20 ปี ในงานก่อสร้างและวิศวกรรม"
              }
            </p>

            {/* Social Media */}
            <div className="flex space-x-3">
              {[
                { icon: Facebook, color: "hover:text-blue-400", href: "#" },
                { icon: Instagram, color: "hover:text-pink-400", href: "#" },
                { icon: Linkedin, color: "hover:text-blue-600", href: "#" },
                { icon: Youtube, color: "hover:text-red-500", href: "#" },
              ].map((social, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    isIconOnly
                    variant="light"
                    className={`text-gray-400 ${social.color} hover:bg-white/5 transition-all duration-300 rounded-full`}
                    as={Link}
                    href={social.href}
                  >
                    <social.icon size={20} />
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {currentLang === 'en' ? 'Quick Links' : 'ลิงก์ด่วน'}
            </h3>
            <ul className="space-y-4">
              {quickLinks.map((link, index) => (
                <motion.li
                  key={link.href}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    {currentLang === 'en' ? link.label : link.labelTh}
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Services */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {currentLang === 'en' ? 'Our Services' : 'บริการของเรา'}
            </h3>
            <ul className="space-y-4">
              {services.map((service, index) => (
                <motion.li
                  key={service.href}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Link
                    href={service.href}
                    className="text-gray-300 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    {currentLang === 'en' ? service.label : service.labelTh}
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {currentLang === 'en' ? 'Contact Info' : 'ข้อมูลติดต่อ'}
            </h3>
            <div className="space-y-6">
              {[
                {
                  icon: MapPin,
                  content: currentLang === 'en'
                    ? "123 Business District, Bangkok 10110, Thailand"
                    : "123 เขตธุรกิจ กรุงเทพมหานคร 10110 ประเทศไทย",
                  color: "text-blue-400"
                },
                {
                  icon: Phone,
                  content: "+66 2 123 4567",
                  color: "text-green-400"
                },
                {
                  icon: Mail,
                  content: "<EMAIL>",
                  color: "text-purple-400"
                },
                {
                  icon: Clock,
                  content: currentLang === 'en'
                    ? "Mon - Fri: 8:00 AM - 6:00 PM\nSat: 8:00 AM - 12:00 PM"
                    : "จันทร์ - ศุกร์: 08:00 - 18:00 น.\nเสาร์: 08:00 - 12:00 น.",
                  color: "text-orange-400"
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start space-x-4 group"
                >
                  <div className={`${item.color} mt-1 flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                    <item.icon size={20} />
                  </div>
                  <div className="text-gray-300 group-hover:text-white transition-colors duration-300">
                    {item.content.split('\n').map((line, lineIndex) => (
                      <p key={lineIndex} className="text-sm leading-relaxed">
                        {line}
                      </p>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Divider with gradient */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-700/50 to-transparent h-px"></div>
      </div>

      {/* Bottom Footer */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0"
        >
          <div className="text-gray-400 text-sm text-center md:text-left">
            © 2024 Padungsilpa Group. {currentLang === 'en' ? 'All rights reserved.' : 'สงวนลิขสิทธิ์'}
          </div>

          <div className="flex items-center space-x-8 text-sm">
            <Link
              href="/privacy"
              className="text-gray-400 hover:text-white transition-colors duration-300 hover:underline underline-offset-4"
            >
              {currentLang === 'en' ? 'Privacy Policy' : 'นโยบายความเป็นส่วนตัว'}
            </Link>
            <Link
              href="/terms"
              className="text-gray-400 hover:text-white transition-colors duration-300 hover:underline underline-offset-4"
            >
              {currentLang === 'en' ? 'Terms of Service' : 'เงื่อนไขการใช้งาน'}
            </Link>
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
              <Button
                isIconOnly
                variant="light"
                size="sm"
                onPress={() => setCurrentLang(currentLang === 'en' ? 'th' : 'en')}
                className="text-gray-400 hover:text-white hover:bg-white/5 transition-all duration-300 rounded-full"
              >
                <Globe size={16} />
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Scroll to Top Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        viewport={{ once: true }}
        className="fixed bottom-8 right-8 z-50"
      >
        <motion.button
          onClick={scrollToTop}
          whileHover={{ scale: 1.1, y: -2 }}
          whileTap={{ scale: 0.95 }}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <ArrowUp size={20} />
        </motion.button>
      </motion.div>
    </footer>
  );
}
