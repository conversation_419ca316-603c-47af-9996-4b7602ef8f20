"use client";

import Section from "@/components/ui/Section";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

// Counter animation hook
const useCounter = (end: number, duration: number = 2000, inView: boolean = false) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!inView) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, inView]);

  return count;
};

export default function StatsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.3 });

  const stats = [
    {
      number: 300,
      suffix: "+",
      label: "ก่อสร้างสถานีบริการน้ำมัน",
      sublabel: "สถานีบริการน้ำมัน",
      icon: "🏗️",
      color: "from-blue-500 to-cyan-500"
    },
    {
      number: 20000,
      suffix: "+",
      label: "ผลิตถังน้ำมันใต้ดิน PERMATANK®",
      sublabel: "ใบ",
      icon: "🛢️",
      color: "from-green-500 to-emerald-500"
    },
    {
      number: 300,
      suffix: "+",
      label: "ท่อน้ำมันใต้ดินผนัง 2 ชั้น",
      sublabel: "สถานีบริการน้ำมัน",
      icon: "🔧",
      color: "from-purple-500 to-violet-500"
    },
    {
      number: 50,
      suffix: "+",
      label: "ระบบวัดน้ำมันอัตโนมัติ (ATG)",
      sublabel: "ระบบ",
      icon: "📊",
      color: "from-orange-500 to-red-500"
    },
  ];

  return (
    <Section background="gradient" padding="xl">
      <div ref={ref} className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full px-6 py-3 mb-6">
            <span className="text-2xl">📈</span>
            <span className="text-gray-700 font-medium">ผลงานที่ภาคภูมิใจ</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              ตัวเลขที่พูดแทนเรา
            </span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ประสบการณ์กว่า 20 ปี ในการให้บริการด้านสถานีบริการน้ำมัน
            พร้อมผลงานที่น่าประทับใจ
          </p>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {stats.map((stat, index) => {
          const count = useCounter(stat.number, 2000, isInView);

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.02 }}
              className="relative group"
            >
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-white/20 group-hover:border-white/40">
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>

                {/* Icon */}
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>

                {/* Number */}
                <div className={`text-5xl md:text-6xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-2`}>
                  {isInView ? count.toLocaleString() : "0"}{stat.suffix}
                </div>

                {/* Label */}
                <div className="text-gray-700 font-semibold text-lg mb-1">
                  {stat.label}
                </div>

                {/* Sublabel */}
                <div className="text-gray-500 text-sm">
                  {stat.sublabel}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </Section>
  );
}
