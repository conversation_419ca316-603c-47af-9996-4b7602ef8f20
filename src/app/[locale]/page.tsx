"use client";

import MainLayout from "@/components/layout/MainLayout";
import StatsSection from "@/components/sections/home/<USER>";
import ServicesSection from "@/components/sections/home/<USER>";
import AboutSection from "@/components/sections/home/<USER>";
import PartnersSection from "@/components/sections/home/<USER>";
import CTASection from "@/components/sections/home/<USER>";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, Link } from "@heroui/react";
import { ArrowRight, Play, CheckCircle, Star } from "lucide-react";
import { useTranslations } from "next-intl";

export default function Home() {
  const t = useTranslations();

  return (
    <MainLayout>
      {/* Video Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center overflow-hidden"
        aria-label="Hero section"
        role="banner"
      >
        {/* Video Background */}
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
          preload="metadata"
        >
          <source
            src="https://charindas19.github.io/my-videos/Padungsilpa-GroupTH.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>

        {/* Video Overlay */}
        <div className="absolute inset-0 bg-black/40"></div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full px-6 py-3 mb-8 shadow-lg"
            >
              <Star className="w-5 h-5 text-yellow-400 fill-current" />
              <span className="text-white text-sm font-medium">
                {t("hero.badge") || "20+ Years of Excellence"}
              </span>
            </motion.div>

            {/* Main Heading */}
            {/* <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-4xl sm:text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-6 leading-tight drop-shadow-2xl"
            >
              <span className="text-white drop-shadow-lg">
                กลุ่มบริษัท
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-white bg-clip-text text-transparent drop-shadow-lg">
                ผดุงศิลป์
              </span>
            </motion.h1> */}

            {/* Subtitle */}
            {/* <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="text-lg sm:text-xl md:text-2xl text-white/95 max-w-4xl mx-auto mb-8 leading-relaxed px-4 drop-shadow-lg"
            >
              ผู้นำด้านการก่อสร้างและวิศวกรรมสถานีบริการน้ำมัน
              <br className="hidden md:block" />
              ด้วยประสบการณ์กว่า 20 ปี เราให้บริการครบวงจรทั่วประเทศไทย
            </motion.p> */}

            {/* Stats */}
            {/* <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-wrap justify-center gap-8 mb-12"
            >
              {[
                { number: "500+", label: "โครงการ" },
                { number: "20+", label: "ปีประสบการณ์" },
                { number: "100%", label: "ความพึงพอใจ" },
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center bg-white/10 backdrop-blur-md rounded-2xl px-6 py-4 border border-white/20 shadow-lg"
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="text-3xl md:text-4xl font-bold text-white mb-1 drop-shadow-lg">
                    {stat.number}
                  </div>
                  <div className="text-white/80 text-sm font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div> */}

            {/* CTA Buttons */}
            {/* <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  as={Link}
                  href="/products-services"
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:from-blue-700 focus:to-purple-700 focus:ring-4 focus:ring-blue-500/50 text-white font-semibold px-8 py-4 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 group backdrop-blur-sm"
                >
                  เรียนรู้เพิ่มเติม
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  as={Link}
                  href="/contact-us"
                  variant="bordered"
                  size="lg"
                  className="border-2 border-white/40 text-white hover:bg-white/20 focus:bg-white/20 focus:ring-4 focus:ring-white/25 backdrop-blur-md font-semibold px-8 py-4 rounded-full transition-all duration-300 group shadow-lg"
                >
                  <Play className="mr-2 w-5 h-5" />
                  ติดต่อเรา
                </Button>
              </motion.div>
            </motion.div> */}
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center backdrop-blur-sm bg-white/10 shadow-lg"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-white/80 rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </section>

      {/* Modern Brand Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <CheckCircle className="w-4 h-4" />
              ความเชี่ยวชาญที่เชื่อถือได้
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                เราคือผู้เชี่ยวชาญ
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                ด้านสถานีบริการน้ำมัน
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              ด้วยประสบการณ์กว่า 20 ปี เราให้บริการก่อสร้าง วิศวกรรม และบำรุงรักษา
              สถานีบริการน้ำมันครบวงจรทั่วประเทศไทย พร้อมทีมงานมืออาชีพและเทคโนโลยีที่ทันสมัย
            </p>
          </motion.div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "ประสบการณ์ยาวนาน",
                description: "มากกว่า 20 ปีในการให้บริการด้านสถานีบริการน้ำมัน",
                icon: "🏆",
              },
              {
                title: "บริการครบวงจร",
                description: "ตั้งแต่การออกแบบ ก่อสร้าง จนถึงการบำรุงรักษา",
                icon: "🔧",
              },
              {
                title: "มาตรฐานสากล",
                description: "ปฏิบัติตามมาตรฐานความปลอดภัยระดับสากล",
                icon: "✅",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Services Section */}
      <ServicesSection />

      {/* About Section */}
      <AboutSection />

      {/* Partners Section */}
      <PartnersSection />

      {/* CTA Section */}
      <CTASection />
    </MainLayout >
  );
}
