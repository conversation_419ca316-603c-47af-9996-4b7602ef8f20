"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Phone, Mail, MessageCircle, ArrowRight, Zap } from "lucide-react";
import { useTranslations } from "next-intl";
import Section from "@/components/ui/Section";
import { motion } from "framer-motion";

export default function CTASection() {
  const t = useTranslations();

  return (
    <Section background="dark" padding="xl">
      <div className="relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative z-10 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Badge */}
            <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span className="text-white/90 font-medium">พร้อมให้บริการ</span>
            </div>

            {/* Main Heading */}
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                {t('home.cta.title') || "พร้อมเริ่มโครงการ"}
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                ของคุณแล้วหรือยัง?
              </span>
            </h2>

            {/* Description */}
            <p className="text-xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed">
              {t('home.cta.description') || "ติดต่อเราวันนี้เพื่อรับคำปรึกษาฟรี และเริ่มต้นโครงการสถานีบริการน้ำมันของคุณกับผู้เชี่ยวชาญที่มีประสบการณ์กว่า 20 ปี"}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 group"
                  startContent={
                    <motion.div
                      whileHover={{ scale: 1.2, rotate: 10 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Phone size={20} />
                    </motion.div>
                  }
                  as="a"
                  href="tel:+6621234567"
                >
                  {t('common.callNow') || "โทรเลย"}
                  <motion.div
                    className="ml-2"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ArrowRight size={16} />
                  </motion.div>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="bordered"
                  size="lg"
                  className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold px-8 py-4 rounded-full transition-all duration-300 group"
                  startContent={
                    <motion.div
                      whileHover={{ scale: 1.2, rotate: -10 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Mail size={20} />
                    </motion.div>
                  }
                  as="a"
                  href="/contact-us"
                >
                  {t('common.getQuote') || "ขอใบเสนอราคา"}
                </Button>
              </motion.div>
            </div>

            {/* Contact Info Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {[
                {
                  icon: Phone,
                  title: "โทรศัพท์",
                  content: "+66 2 123 4567",
                  href: "tel:+6621234567",
                  color: "from-green-500 to-emerald-500"
                },
                {
                  icon: Mail,
                  title: "อีเมล",
                  content: "<EMAIL>",
                  href: "mailto:<EMAIL>",
                  color: "from-blue-500 to-cyan-500"
                },
                {
                  icon: MessageCircle,
                  title: "Line Official",
                  content: "@padungsilpa",
                  href: "#",
                  color: "from-purple-500 to-violet-500"
                }
              ].map((contact, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5, scale: 1.02 }}
                >
                  <a
                    href={contact.href}
                    className="block bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all duration-300 group"
                  >
                    <div className={`w-12 h-12 bg-gradient-to-r ${contact.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <contact.icon size={24} className="text-white" />
                    </div>
                    <h3 className="text-white font-semibold mb-2">{contact.title}</h3>
                    <p className="text-white/70 text-sm group-hover:text-white transition-colors duration-300">
                      {contact.content}
                    </p>
                  </a>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </Section>
  );
}
