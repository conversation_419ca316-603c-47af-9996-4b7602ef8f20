"use client";

import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { Award, ArrowRight, CheckCircle, Shield, Target, Users2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Section from "@/components/ui/Section";
import { motion } from "framer-motion";

export default function AboutSection() {
  const t = useTranslations();

  const features = [
    {
      text: t('home.about.features.team') || "ทีมงานมืออาชีพ",
      icon: Users2,
      color: "text-blue-500"
    },
    {
      text: t('home.about.features.quality') || "คุณภาพมาตรฐานสากล",
      icon: Shield,
      color: "text-green-500"
    },
    {
      text: t('home.about.features.support') || "บริการหลังการขาย",
      icon: Target,
      color: "text-purple-500"
    },
    {
      text: t('home.about.features.coverage') || "ครอบคลุมทั่วประเทศ",
      icon: CheckCircle,
      color: "text-orange-500"
    },
  ];

  return (
    <Section background="gradient" padding="xl">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full px-6 py-3 mb-6">
            <span className="text-2xl">🏢</span>
            <span className="text-gray-700 font-medium">เกี่ยวกับเรา</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              {t('home.about.title') || "ผู้เชี่ยวชาญ"}
            </span>
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              ด้านสถานีบริการน้ำมัน
            </span>
          </h2>

          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            {t('home.about.description') || "เราคือผู้นำด้านการก่อสร้างและวิศวกรรมสถานีบริการน้ำมัน ด้วยประสบการณ์กว่า 20 ปี และทีมงานมืออาชีพ เราพร้อมให้บริการครบวงจรตั้งแต่การออกแบบ ก่อสร้าง จนถึงการบำรุงรักษา"}
          </p>

          <div className="space-y-6 mb-10">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center space-x-4 group"
              >
                <div className={`w-12 h-12 ${feature.color} bg-white rounded-xl shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon size={24} />
                </div>
                <span className="text-gray-700 text-lg font-medium group-hover:text-gray-900 transition-colors duration-300">
                  {feature.text}
                </span>
              </motion.div>
            ))}
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group"
              endContent={
                <motion.div
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <ArrowRight size={20} />
                </motion.div>
              }
              as="a"
              href="/pds-group"
            >
              {t('common.learnMore') || "เรียนรู้เพิ่มเติม"}
            </Button>
          </motion.div>
        </motion.div>

        {/* Image */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="relative"
        >
          <div className="relative overflow-hidden rounded-3xl shadow-2xl group">
            <Image
              src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
              alt="Construction team"
              className="w-full h-[500px] object-cover group-hover:scale-105 transition-transform duration-700"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          {/* Floating Card */}
          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.8 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
            whileHover={{ y: -5, scale: 1.05 }}
            className="absolute -bottom-8 -left-8 bg-white/95 backdrop-blur-sm p-8 rounded-2xl shadow-2xl border border-white/20"
          >
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Award size={32} className="text-white" />
              </div>
              <div>
                <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  ISO 9001
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  {t('home.about.certification') || "มาตรฐานคุณภาพสากล"}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Background Elements */}
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500/10 rounded-full blur-xl"></div>
          <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-purple-500/10 rounded-full blur-xl"></div>
        </motion.div>
      </div>
    </Section>
  );
}
