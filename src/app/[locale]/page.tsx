"use client";

import MainLayout from "@/components/layout/MainLayout";
import StatsSection from "@/components/sections/home/<USER>";
import ServicesSection from "@/components/sections/home/<USER>";
import AboutSection from "@/components/sections/home/<USER>";
import PartnersSection from "@/components/sections/home/<USER>";
import CTASection from "@/components/sections/home/<USER>";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { ArrowRight, Play, CheckCircle, Star } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";

export default function Home() {
  const t = useTranslations();

  return (
    <MainLayout>
      {/* Professional Hero Section */}
      <section
        className="relative min-h-screen flex items-center bg-white"
        aria-label="Hero section"
        role="banner"
      >
        {/* Video Background - Subtle */}
        <div className="absolute inset-0 overflow-hidden">
          <video
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 w-full h-full object-cover opacity-20"
            preload="metadata"
          >
            <source
              src="https://charindas19.github.io/my-videos/Padungsilpa-GroupTH.mp4"
              type="video/mp4"
            />
            Your browser does not support the video tag.
          </video>
          <div className="absolute inset-0 bg-white/80"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen py-20">

            {/* Left Content */}
            <div className="space-y-8">
              {/* Company Badge */}
              <div className="inline-flex items-center gap-3 bg-blue-50 border border-blue-100 rounded-lg px-4 py-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-blue-700 text-sm font-medium">ก่อตั้งเมื่อปี 2003</span>
              </div>

              {/* Main Heading */}
              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  กลุ่มบริษัท
                  <br />
                  <span className="text-blue-600">ผดุงศิลป์</span>
                </h1>
                <div className="w-20 h-1 bg-blue-600"></div>
              </div>

              {/* Subtitle */}
              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                ผู้นำด้านการก่อสร้างและวิศวกรรมสถานีบริการน้ำมัน
                ด้วยประสบการณ์กว่า 20 ปี เราให้บริการครบวงจรทั่วประเทศไทย
              </p>

              {/* Key Points */}
              <div className="space-y-3">
                {[
                  "มาตรฐาน ISO 9001:2015",
                  "ทีมงานมืออาชีพกว่า 200 คน",
                  "โครงการสำเร็จกว่า 500 แห่ง"
                ].map((point, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{point}</span>
                  </div>
                ))}
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  as={Link}
                  href="/products-services"
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  ดูบริการของเรา
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>

                <Button
                  as={Link}
                  href="/contact-us"
                  variant="bordered"
                  size="lg"
                  className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 font-medium px-8 py-3 rounded-lg transition-all duration-300"
                >
                  ติดต่อเรา
                </Button>
              </div>
            </div>

            {/* Right Content - Stats Grid */}
            <div className="grid grid-cols-2 gap-6">
              {[
                { number: "500+", label: "โครงการที่สำเร็จ", sublabel: "สถานีบริการน้ำมัน" },
                { number: "20+", label: "ปีประสบการณ์", sublabel: "ในอุตสาหกรรม" },
                { number: "200+", label: "ทีมงานมืออาชีพ", sublabel: "วิศวกรและช่างเทคนิค" },
                { number: "100%", label: "ความพึงพอใจ", sublabel: "จากลูกค้า" }
              ].map((stat, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <div className="text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
                  <div className="text-gray-900 font-medium mb-1">{stat.label}</div>
                  <div className="text-sm text-gray-500">{stat.sublabel}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-gray-400 rounded-full mt-2"
            />
          </motion.div>
        </div>
      </section>

      {/* Company Overview Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-flex items-center gap-2 text-blue-600 text-sm font-medium">
                  <div className="w-8 h-px bg-blue-600"></div>
                  เกี่ยวกับเรา
                </div>

                <h2 className="text-4xl font-bold text-gray-900 leading-tight">
                  ผู้เชี่ยวชาญด้าน
                  <br />
                  สถานีบริการน้ำมัน
                </h2>

                <p className="text-lg text-gray-600 leading-relaxed">
                  กลุ่มบริษัท ผดุงศิลป์ เป็นผู้นำด้านการก่อสร้างและวิศวกรรมสถานีบริการน้ำมัน
                  ด้วยประสบการณ์กว่า 20 ปี เราได้สร้างความเชื่อมั่นให้กับลูกค้าทั่วประเทศไทย
                </p>
              </div>

              {/* Key Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {[
                  {
                    title: "ประสบการณ์ยาวนาน",
                    description: "มากกว่า 20 ปีในอุตสาหกรรม",
                    icon: "📈"
                  },
                  {
                    title: "บริการครบวงจร",
                    description: "ตั้งแต่ออกแบบถึงบำรุงรักษา",
                    icon: "🔧"
                  },
                  {
                    title: "มาตรฐานสากล",
                    description: "ISO 9001:2015 Certified",
                    icon: "🏆"
                  },
                  {
                    title: "ทีมมืออาชีพ",
                    description: "วิศวกรและช่างเทคนิคชำนาญ",
                    icon: "👥"
                  }
                ].map((feature, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{feature.icon}</span>
                      <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                    </div>
                    <p className="text-sm text-gray-600 ml-11">{feature.description}</p>
                  </div>
                ))}
              </div>

              {/* CTA */}
              <div className="pt-4">
                <Button
                  as={Link}
                  href="/pds-group"
                  variant="bordered"
                  className="border-blue-600 text-blue-600 hover:bg-blue-50 font-medium"
                >
                  เรียนรู้เพิ่มเติมเกี่ยวกับเรา
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Right Content - Image */}
            <div className="relative">
              <div className="aspect-[4/3] bg-gray-200 rounded-2xl overflow-hidden shadow-lg">
                <Image
                  src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                  alt="Construction team"
                  width={2070}
                  height={1380}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Floating Card */}
              <div className="absolute -bottom-6 -left-6 bg-white rounded-xl p-6 shadow-xl border border-gray-100">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Star className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">ISO 9001</div>
                    <div className="text-sm text-gray-600">Quality Management</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Services Section */}
      <ServicesSection />

      {/* About Section */}
      <AboutSection />

      {/* Partners Section */}
      <PartnersSection />

      {/* CTA Section */}
      <CTASection />
    </MainLayout >
  );
}
