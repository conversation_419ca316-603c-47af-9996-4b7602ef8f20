"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody } from "@heroui/react";
import { Building2, <PERSON><PERSON>, Users, ArrowRight, Sparkles } from "lucide-react";
import { useTranslations } from "next-intl";
import Section from "@/components/ui/Section";
import { motion } from "framer-motion";

export default function ServicesSection() {
  const t = useTranslations();

  const services = [
    {
      icon: Building2,
      title: t('home.services.gasStation.title') || "บริการสถานีบริการน้ำมัน",
      description: t('home.services.gasStation.description') || "ออกแบบและก่อสร้างสถานีบริการน้ำมันครบวงจร",
      gradient: "from-blue-500 to-cyan-500",
      bgGradient: "from-blue-50 to-cyan-50",
      features: ["ออกแบบสถานี", "ก่อสร้าง", "ติดตั้งอุปกรณ์"]
    },
    {
      icon: Wrench,
      title: t('home.services.engineering.title') || "งานวิศวกรรม",
      description: t('home.services.engineering.description') || "บริการด้านวิศวกรรมและเทคนิคเฉพาะทาง",
      gradient: "from-green-500 to-emerald-500",
      bgGradient: "from-green-50 to-emerald-50",
      features: ["วิศวกรรมโครงสร้าง", "ระบบไฟฟ้า", "ระบบประปา"]
    },
    {
      icon: Users,
      title: t('home.services.maintenance.title') || "บำรุงรักษา",
      description: t('home.services.maintenance.description') || "บริการบำรุงรักษาและซ่อมแซมอุปกรณ์",
      gradient: "from-purple-500 to-violet-500",
      bgGradient: "from-purple-50 to-violet-50",
      features: ["ตรวจสอบระบบ", "ซ่อมแซม", "อัพเกรด"]
    },
  ];

  return (
    <Section background="white" padding="xl">
      <div className="text-center mb-20">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-full px-6 py-3 mb-6">
            <Sparkles className="w-5 h-5 text-blue-600" />
            <span className="text-blue-700 font-medium">บริการของเรา</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              {t('home.services.title') || "บริการครบวงจร"}
            </span>
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              สำหรับสถานีบริการน้ำมัน
            </span>
          </h2>

          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('home.services.description') || "เราให้บริการครบวงจรตั้งแต่การออกแบบ ก่อสร้าง จนถึงการบำรุงรักษา ด้วยทีมงานมืออาชีพและเทคโนโลยีที่ทันสมัย"}
          </p>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {services.map((service, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, scale: 1.02 }}
            className="group"
          >
            <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-white">
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>

              <CardBody className="relative z-10 p-8 text-center">
                {/* Icon Container */}
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className={`relative w-20 h-20 bg-gradient-to-br ${service.gradient} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                >
                  <service.icon size={36} className="text-white" />
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </motion.div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">
                  {service.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 mb-6 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                  {service.description}
                </p>

                {/* Features */}
                <div className="flex flex-wrap justify-center gap-2 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <span
                      key={featureIndex}
                      className={`px-3 py-1 text-xs font-medium bg-gradient-to-r ${service.gradient} text-white rounded-full opacity-80 group-hover:opacity-100 transition-opacity duration-300`}
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* CTA Button */}
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="light"
                    className={`bg-gradient-to-r ${service.gradient} text-white hover:shadow-lg transition-all duration-300 group-hover:shadow-xl`}
                    endContent={
                      <motion.div
                        whileHover={{ x: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ArrowRight size={16} />
                      </motion.div>
                    }
                  >
                    {t('common.learnMore') || "เรียนรู้เพิ่มเติม"}
                  </Button>
                </motion.div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </Section>
  );
}
